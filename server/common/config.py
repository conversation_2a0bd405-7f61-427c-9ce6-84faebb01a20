import os
from typing import Dict

from dotenv import load_dotenv

load_dotenv()

# Service API keys configuration
SERVICE_API_KEYS: Dict[str, str] = {
    "gemini": os.getenv("GEMINI_API_KEY", ""),
    "openai": os.getenv("OPENAI_API_KEY", ""),
    "daily": os.getenv("DAILY_API_KEY", ""),
    "aws": os.getenv("AWS_ACCESS_KEY_ID", ""),  # For AWS services
}

# Simple bot configuration (will be enhanced when pipecat is available)
DEFAULT_BOT_CONFIG = {
    "services": {
        "llm": "gemini",  # Default to Gemini for HTTP pipeline
        "tts": "cartesia",
        "stt": "deepgram",
    },
    "config": [
        {
            "service": "llm",
            "options": [
                {"name": "model", "value": "gemini-2.0-flash-exp"},
                {"name": "initial_messages", "value": []},
                {"name": "run_on_config", "value": True},
            ],
        },
        {
            "service": "tts",
            "options": [
                {"name": "voice", "value": "79a125e8-cd45-4c13-8a67-188112f4dd22"},
            ],
        },
        {
            "service": "stt",
            "options": [
                {"name": "model", "value": "nova-2"},
                {"name": "language", "value": "en"},
            ],
        },
    ],
}

# Language code mapping
LANGUAGE_CODES = {
    "en": "english",
    "es": "spanish",
    "fr": "french",
    "de": "german",
    "it": "italian",
    "pt": "portuguese",
    "ru": "russian",
    "ja": "japanese",
    "ko": "korean",
    "zh": "chinese",
}


def get_language_code(lang: str) -> str:
    """Get language code from language string"""
    return LANGUAGE_CODES.get(lang.lower(), "english")
