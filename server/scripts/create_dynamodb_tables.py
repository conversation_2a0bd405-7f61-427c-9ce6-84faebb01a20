#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create DynamoDB tables for the Voice Agent application.
"""

import asyncio
import os
import sys

# Add the parent directory to the path so we can import from server
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.dynamodb import default_dynamodb_factory


async def main():
    """Create DynamoDB tables if they don't exist"""
    print("Initializing DynamoDB tables...")
    await default_dynamodb_factory.initialize_tables()
    print("DynamoDB tables initialized successfully!")


if __name__ == "__main__":
    asyncio.run(main())
