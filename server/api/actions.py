from fastapi import API<PERSON>outer, HTTPException, status
from fastapi.responses import StreamingResponse
from loguru import logger

from bots.http.bot import http_bot_pipeline
from bots.types import BotParams
from common.config import DEFAULT_BOT_CONFIG
from common.dynamodb import (
    default_dynamodb_factory,
    get_conversation_by_id,
    get_messages_by_conversation_id,
    ATTACHMENTS_TABLE,
)
from common.models import Attachment

router = APIRouter(prefix="/bot")


@router.post("/action", response_class=StreamingResponse)
async def stream_action(
    params: BotParams,
) -> StreamingResponse:
    """
    Single-turn HTTP action endpoint.

    This endpoint initiates a streaming response and returns chunks of the bot's
    response in real-time using server-sent events.

    Args:
        params (BotParams): Parameters for the bot interaction, must include:
            - conversation_id: UUID of the conversation to process
            - attachments: List of attachment IDs to include in the bot's response

    Returns:
        StreamingResponse: A streaming response with media type "text/event-stream"
            containing the bot's response chunks.

    Raises:
        HTTPException (400): When conversation_id is missing from params.
        HTTPException (404): When the specified conversation is not found.
        HTTPException (400): When service validation fails (via _validate_services).

    Flow:
        1. Validates the presence of conversation_id
        2. Checks if the conversation exists in the database
        3. Retrieves conversation history
        4. Validates bot services configuration
        5. Runs the bot pipeline and streams the response
    """
    if not params.conversation_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Missing conversation_id in params",
        )

    # Early exit check that conversation exists before initiating a stream response
    conversation_item = await get_conversation_by_id(params.conversation_id)
    if conversation_item is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Conversation {params.conversation_id} not found",
        )

    config = DEFAULT_BOT_CONFIG

    # Retrieve attachments from database
    attachments = []
    if params.attachments:
        try:
            table = default_dynamodb_factory.get_table(ATTACHMENTS_TABLE)
            for attachment_id in params.attachments:
                response = table.get_item(Key={"attachment_id": attachment_id})
                if "Item" in response:
                    attachment_item = response["Item"]
                    attachments.append(Attachment.from_dynamodb(attachment_item))
            logger.debug(f"Retrieved {len(attachments)} attachments")
        except Exception as e:
            logger.error(f"Error retrieving attachments: {str(e)}")

    async def generate():
        try:
            # Retrieve any existing messages for the conversation
            message_items = await get_messages_by_conversation_id(
                params.conversation_id
            )
            messages = [msg.get("content", {}) for msg in message_items]

            # Run the single turn pipeline and yield text frames
            gen, task = await http_bot_pipeline(params, config, messages, attachments)
            async for chunk in gen:
                yield chunk
            await task
        except Exception as e:
            logger.error(f"Error in HTTP bot pipeline: {str(e)}")
            # Yield error message in SSE format
            yield f"data: Error: {str(e)}\n\n"

    return StreamingResponse(generate(), media_type="text/event-stream")
